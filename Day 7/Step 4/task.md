### TODO-1: 
- Create a variable called `lives` to keep track of the number of lives left.
- Set `lives` to equal `6`.


### TODO-2: 
- If `guess` is not a letter in the `chosen_word`, Then reduce `lives` by `1`. 
- If `lives` goes down to `0` then the game should end, and it should print "You lose."

<div class="hint">
  The not in keywords will be your friend in this todo. You can check if something exists in the chosen_word completely separately from the rest of the code.
</div>


### TODO-3: 
- print the ASCII art from the list `stages` that corresponds to the current number of `lives` the user has remaining.
