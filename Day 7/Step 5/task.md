### TODO-1: 
- Update the word list to use the `word_list` from hangman_words.py

### TODO-2: 
- Update the code to use the `stages` from the file hangman_art.py

### TODO-3: 
- Import the `logo` from hangman_art.py and print it at the start of the game.

### TODO-4: 
- If the user has entered a letter they've already guessed, print the letter and let them know.
- We should not deduct a life for this.
- e.g. You've already guessed a

### TODO-5: 
- If the letter is not in the chosen_word, print out the letter and let them know it's not in the word.
- e.g. You guessed d, that's not in the word. You lose a life.

### TODO-6: 
- Update the code below to tell the user how many lives they have left.
```print("****************************<???>/6 LIVES LEFT****************************")```

### TODO 7: 
- Update the print statement to give the user the correct word they were trying to guess.
- e.g. `IT WAS <Correct Word>! YOU LOSE`