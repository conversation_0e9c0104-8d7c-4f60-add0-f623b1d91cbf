The modulo operator gives you the remainder of a division.

`6 % 2 #will be 0`

`6 % 5 #will be 1`

`6 % 4 #will be 2`

### PAUSE 1 - What is 10 % 3?
What do you think this will output?

`print(10 % 3)`

### PAUSE 2 - Check Odd or Even
Write some code using what you have learnt about the modulo operator and conditional checks in Python to check if the number in he input area is odd or even.
If it's odd print out the word "Odd" otherwise printout "Even".

<div class="hint">
1. First get the user input using the input() function.
<br/>2. Next, convert the input into an int using the int() function.
<br/>3. Now store the number in a variable. 
<br/>4. Use the modulo (%) to check if the user input number can be divided cleanly by 2. 
<br>5. Use if/else to check if the result of the modulo check in step 4 is equal to 0 then that input number is even, otherwise it's odd. 
</div>
