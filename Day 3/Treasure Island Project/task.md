Your goal today is to build a "Chose your own adventure game".
Using what you have learnt in the lessons today you will be building a very simple version of this type of text game. 

Use the flow chart [linked here](https://www.draw.io/?lightbox=1&highlight=0000ff&edit=_blank&layers=1&nav=1&title=Treasure%20Island%20Conditional.drawio#Uhttps%3A%2F%2Fdrive.google.com%2Fuc%3Fid%3D1oDe4ehjWZipYRsVfeAx2HyB7LCQ8_Fvi%26export%3Ddownload) to create the game logic. 

Once you've completed the project, you can always extend the game and make it more interesting!

<div class="hint">
  You can use the lower() function to turn any string into all lower case. 
https://www.w3schools.com/python/ref_string_lower.asp

Alternatively you can also use the logical operator "or" to check for other spellings of user choice. e.g. Right, right or RIGHT.
</div>


### Demo
https://appbrewery.github.io/python-day3-demo/